<!--
 * @Author: songjin<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-15 16:29:02
 * @LastEditors: songjingyang <EMAIL>
 * @LastEditTime: 2025-08-01 15:49:56
 * @FilePath: /mb-users-magic-brush/src/components/ChatAiTool/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="ai-tool" @click="close" v-if="filteredTools.length">
    <div class="content" @click.stop>
      <div class="tools-container">
        <div class="tools">
          <div @click="chooseTool(item.functionName)" class="item" v-for="item in filteredTools"
            :key="item.functionName">
            <span>{{ item.functionName }}</span>
            <span class="tips">&emsp;{{ item.tips }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import useChatStore from '../../store/modules/chat'
import { computed } from 'vue'

const chatStore = useChatStore()

defineOptions({
  name: 'ChatAiTool',
})

const chooseTool = (tool: string) => {
  chatStore.tool = tool
  close()
}

const close = () => {
  chatStore.setShowAiTool(false)
}

const filteredTools = computed(() => {
  const tools = chatStore.tools?.flatMap(item => item.toolList)
  return tools.filter(item => item.functionName.includes(chatStore.filterKeyword))
})
</script>

<style lang="scss" scoped>
.ai-tool {
  position: absolute;
  bottom: 120%;
  left: 0;
  width: 100%;
  z-index: 100;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;

  .content {
    width: 100%;
    // background: #ffffff;
    background-color: #d00;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    border-radius: 15px;
    max-height: 8; // 限制最大高度，确保能够滚动
    overflow: hidden; // 隐藏溢出，由内部容器处理滚动

    // 滚动容器
    .tools-container {
      height: 100%;
      max-height: 400px;
      overflow-y: auto;
      overflow-x: hidden;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;

        &:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      // 平滑滚动
      scroll-behavior: smooth;
    }

    .tools {
      margin: 0 25px;
      padding-top: 25px;
      padding-bottom: 25px; // 确保底部有足够间距

      .item {
        height: 40px;
        margin-bottom: 10px;
        font-size: 18px;
        color: #2266ff;
        cursor: pointer; // 添加鼠标指针样式
        transition: all 0.2s ease; // 添加过渡动画
        border-radius: 8px; // 添加圆角
        padding: 0 8px; // 添加内边距
        margin-left: -8px;
        margin-right: -8px;
        display: flex;
        align-items: center; // 垂直居中对齐

        &:hover {
          background-color: rgba(34, 102, 255, 0.05); // 悬停背景色
        }

        &:last-child {
          margin-bottom: 0; // 最后一项不需要底部间距
        }

        img {
          height: 22px;
          margin-right: 17px;
        }

        .title {
          font-weight: normal;
          font-size: 20px;
          color: #000000;
        }

        .tips {
          color: #909090;
        }
      }
    }
  }
}
</style>
